#%%
import sys
sys.path.insert(0, '../../../')
#%%
from pathlib import Path 
from dotenv import load_dotenv

env_path = Path('../../../crew_trading') / '.env'
load_dotenv(dotenv_path=env_path)
#%%
import pandas as pd
import logging
from agents_backtesting import backtest_agents, print_results
#%%
# Configure the specific logger
logger = logging.getLogger("backtest_framework")
logger.propagate = False  # Prevent propagation to root logger
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

# Other loggers will remain at their default level
logging.basicConfig(level=logging.WARNING)
#%% md
# Load prices and news data
#%%
price_data = pd.read_csv('../../../data/agents_backtest/aapl_prices_daily_2025-03-01_2025-05-01.csv', index_col='Date')
#%%
news_data = pd.read_csv('../../../data/agents_backtest/aapl_news_2025-03-01_2025-05-01.csv', index_col='date', parse_dates=True)
#%% md
 # Config
#%%
models_for_technical_analysis=[
    'openrouter/microsoft/mai-ds-r1:free',
]

models_for_news_analysis=[
    'openrouter/microsoft/mai-ds-r1:free',
]

model_for_decision='openrouter/microsoft/mai-ds-r1:free'

symbol='AAPL'

initial_cash=1000
required_confidence=40
allow_fractional_position=True
position_size_percentage: float = 0.2
output_log_file='mai-ds-r1.json'
delay_seconds = 30
#%%
results, chart = backtest_agents(
                    price_data=price_data,
                    news_data=news_data,
                    models_for_technical_analysis=models_for_technical_analysis,
                    models_for_news_analysis=models_for_news_analysis,
                    model_for_decision=model_for_decision,
                    symbol=symbol,
                    initial_cash=initial_cash,
                    required_confidence=required_confidence,
                    allow_fractional_position=allow_fractional_position,
                    position_size_percentage=position_size_percentage,
                    output_log_file=output_log_file,
                    delay_seconds=delay_seconds
                )
#%%
