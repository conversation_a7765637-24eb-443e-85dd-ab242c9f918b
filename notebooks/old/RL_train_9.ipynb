#%%
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Callable, Dict, List, Optional, Tuple, Type, Union
import torch as th
from torch import nn
import torch.nn.functional as F
import random

from speculator.rl.environment import SingleStockEnvV3
import gym
from gym import spaces

from stable_baselines3 import A2C, DQN, PPO
from stable_baselines3.common.vec_env import DummyVecEnv
from stable_baselines3.common.callbacks import BaseCallback, EvalCallback, StopTrainingOnNoModelImprovement
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.policies import ActorCriticPolicy
#%%
class CustomCallback(BaseCallback):
    """
    A custom callback that derives from ``BaseCallback``.

    :param verbose: Verbosity level: 0 for no output, 1 for info messages, 2 for debug messages
    """
    def __init__(self, window_size, data_length, verbose=0, number_of_envs=1, save_model_path=None, save_model_steps_frequency=None):
        # super(CustomCallback, self).__init__(verbose)
        super(CustomCallback, self).__init__(verbose)
        
        # Those variables will be accessible in the callback
        # (they are defined in the base class)
        # The RL model
        # self.model = None  # type: BaseAlgorithm
        # An alias for self.model.get_env(), the environment used for training
        # self.training_env = None  # type: Union[gym.Env, VecEnv, None]
        # Number of time the callback was called
        # self.n_calls = 0  # type: int
        # self.num_timesteps = 0  # type: int
        # local and global variables
        # self.locals = None  # type: Dict[str, Any]
        # self.globals = None  # type: Dict[str, Any]
        # The logger object, used to report things in the terminal
        # self.logger = None  # stable_baselines3.common.logger
        # # Sometimes, for event callback, it is useful
        # # to have access to the parent object
        # self.parent = None  # type: Optional[BaseCallback]
        # self.env = self.training_env.envs[0]
        self.window_size = window_size
        self.data_length = data_length
        self.number_of_envs = number_of_envs
        self.save_model_path = save_model_path
        self.save_model_steps_frequency = save_model_steps_frequency
        self.episode = 1

    def _on_training_start(self) -> None:
        """
        This method is called before the first rollout starts.
        """
        pass

    def _on_rollout_start(self) -> None:
        """
        A rollout is the collection of environment interaction
        using the current policy.
        This event is triggered before collecting new samples.
        """
        # print("Rollout start")
        pass

    def _on_step(self) -> bool:
        """
        This method will be called by the model after each call to `env.step()`.

        For child callback (of an `EventCallback`), this will be called
        when the event is triggered.

        :return: (bool) If the callback returns False, training is aborted early.
        """
        
        # print(self.n_calls, self.training_env.envs[0].done, self.training_env.envs[0].current_tick, self.training_env.envs[0].end_tick)
        episode = self.episode * self.number_of_envs
        if self.training_env.envs[0].current_tick == self.data_length - 2:
            print(f"Episode: {episode}, Total reward: {self.training_env.envs[0].total_reward}, Total profit: {self.training_env.envs[0].total_profit}")
            self.logger.record("total_reward", self.training_env.envs[0].total_reward)
            self.logger.record("total_profit", self.training_env.envs[0].total_profit)
            self.episode += 1
            
        if self.save_model_path is not None and self.save_model_steps_frequency is not None:
            if self.num_timesteps % self.save_model_steps_frequency == 0:
                print(f'Saving model to {self.save_model_path}')
                self.model.save(self.save_model_path)
        
            
        return True

    def _on_rollout_end(self) -> None:
        """
        This event is triggered before updating the policy.
        """
        # print("Rollout end")
        pass

    def _on_training_end(self) -> None:
        """
        This event is triggered before exiting the `learn()` method.
        """
        pass
#%%
class SaveModelCallback(BaseCallback):
    def __init__(self, path_to_model, verbose=0):
        super(SaveModelCallback, self).__init__(verbose)
        self.path_to_model = path_to_model

    def _on_training_start(self) -> None:
        pass

    def _on_rollout_start(self) -> None:
        pass

    def _on_step(self) -> bool:
        self.model.save(self.path_to_model)
        return True

    def _on_rollout_end(self) -> None:
        pass

    def _on_training_end(self) -> None:
        pass
#%%
def linear_schedule(initial_value: float) -> Callable[[float], float]:
    """
    Linear learning rate schedule.

    :param initial_value: Initial learning rate.
    :return: schedule that computes
      current learning rate depending on remaining progress
    """
    def func(progress_remaining: float) -> float:
        """
        Progress will decrease from 1 (beginning) to 0.

        :param progress_remaining:
        :return: current learning rate
        """
        return progress_remaining * initial_value

    return func
#%%
def setup_seed(seed):
    random.seed(seed)                          
    np.random.seed(seed)                       
    th.manual_seed(seed)                    
    th.cuda.manual_seed(seed)               
    th.cuda.manual_seed_all(seed)           
    th.backends.cudnn.deterministic = True
#%%
class Conv1dModel(nn.Module):
        def __init__(self, num_features, num_targets, hidden_size):
            super(Conv1dModel, self).__init__()
            cha_1 = 128
            cha_2 = 256
            cha_3 = 256

            cha_1_reshape = int(hidden_size/cha_1)
            cha_po_1 = int(hidden_size/cha_1/2)
            cha_po_2 = int(hidden_size/cha_1/2/2) * cha_3

            self.cha_1 = cha_1
            self.cha_2 = cha_2
            self.cha_3 = cha_3
            self.cha_1_reshape = cha_1_reshape
            self.cha_po_1 = cha_po_1
            self.cha_po_2 = cha_po_2

            self.batch_norm1 = nn.BatchNorm1d(num_features)
            self.dropout1 = nn.Dropout(0.05)
            self.dense1 = nn.utils.weight_norm(nn.Linear(num_features, hidden_size))

            self.batch_norm_c1 = nn.BatchNorm1d(cha_1)
            self.dropout_c1 = nn.Dropout(0.05)
            self.conv1 = nn.utils.weight_norm(nn.Conv1d(cha_1,cha_2, kernel_size = 5, stride = 1, padding=2,  bias=False),dim=None)

            self.ave_po_c1 = nn.AdaptiveAvgPool1d(output_size = cha_po_1)

            self.batch_norm_c2 = nn.BatchNorm1d(cha_2)
            self.dropout_c2 = nn.Dropout(0.05)
            self.conv2 = nn.utils.weight_norm(nn.Conv1d(cha_2,cha_2, kernel_size = 3, stride = 1, padding=1, bias=True),dim=None)

            self.batch_norm_c2_1 = nn.BatchNorm1d(cha_2)
            self.dropout_c2_1 = nn.Dropout(0.15)
            self.conv2_1 = nn.utils.weight_norm(nn.Conv1d(cha_2,cha_2, kernel_size = 3, stride = 1, padding=1, bias=True),dim=None)

            self.batch_norm_c2_2 = nn.BatchNorm1d(cha_2)
            self.dropout_c2_2 = nn.Dropout(0.1)
            self.conv2_2 = nn.utils.weight_norm(nn.Conv1d(cha_2,cha_3, kernel_size = 5, stride = 1, padding=2, bias=True),dim=None)

            self.max_po_c2 = nn.MaxPool1d(kernel_size=4, stride=2, padding=1)

            self.flt = nn.Flatten()

            self.batch_norm3 = nn.BatchNorm1d(cha_po_2)
            self.dropout3 = nn.Dropout(0.1)
            self.dense3 = nn.utils.weight_norm(nn.Linear(cha_po_2, num_targets))

        def forward(self, x):

            x = self.batch_norm1(x)
            x = self.dropout1(x)
            x = F.celu(self.dense1(x), alpha=0.06)

            x = x.reshape(x.shape[0],self.cha_1,
                          self.cha_1_reshape)

            x = self.batch_norm_c1(x)
            x = self.dropout_c1(x)
            x = F.relu(self.conv1(x))

            x = self.ave_po_c1(x)

            x = self.batch_norm_c2(x)
            x = self.dropout_c2(x)
            x = F.relu(self.conv2(x))
            x_s = x

            x = self.batch_norm_c2_1(x)
            x = self.dropout_c2_1(x)
            x = F.relu(self.conv2_1(x))

            x = self.batch_norm_c2_2(x)
            x = self.dropout_c2_2(x)
            x = F.relu(self.conv2_2(x))
            x =  x * x_s

            x = self.max_po_c2(x)

            x = self.flt(x)

            x = self.batch_norm3(x)
            x = self.dropout3(x)
            x = self.dense3(x)

            return x
#%%
class CustomNetwork(nn.Module):

    def __init__(
        self,
        feature_dim: int
    ):
        super().__init__()

        # IMPORTANT:
        # Save output dimensions, used to create the distributions
        self.latent_dim_pi = 64
        self.latent_dim_vf = 64
        
        # Policy network
        self.policy_net = Conv1dModel(num_features=feature_dim, hidden_size=512, num_targets=64)
        # Value network
        self.value_net = Conv1dModel(num_features=feature_dim, hidden_size=512, num_targets=64)

    def forward(self, features: th.Tensor) -> Tuple[th.Tensor, th.Tensor]:
        return self.forward_actor(features), self.forward_critic(features)

    def forward_actor(self, features: th.Tensor) -> th.Tensor:
        return self.policy_net(features)

    def forward_critic(self, features: th.Tensor) -> th.Tensor:
        return self.value_net(features)
#%%
class CustomActorCriticPolicy(ActorCriticPolicy):
    def __init__(
        self,
        observation_space: spaces.Space,
        action_space: spaces.Space,
        lr_schedule: Callable[[float], float],
        *args,
        **kwargs,
    ): 
        super().__init__(
            observation_space,
            action_space,
            lr_schedule,
            # Pass remaining arguments to base class
            *args,
            **kwargs,
        )
        # Disable orthogonal initialization
        self.ortho_init = False

    def _build_mlp_extractor(self) -> None:
        self.mlp_extractor = CustomNetwork(self.features_dim)
#%%
def convert_to_bucket(value, bucket_granularity = 2):
    if value < 0:
        negative = True
    else:
        negative = False
    
    bucket = (((np.abs(value) * 100) // bucket_granularity) * bucket_granularity) / 100
    if negative:
        return -1 * bucket
    else:
        return bucket
#%%
def convert_columns_to_buckets(columns, data):
    for col in columns:
        data[col] = data[col].apply(convert_to_bucket)
    return data
#%%
def evaluate_model_using_data(env_name, data, window_size, model):
    test_env = gym.make(env_name, data=data.values, window_size=window_size)
    observation = test_env.reset()
    while True:
        action, _states = model.predict(observation, deterministic=True)
        observation, reward, done, info = test_env.step(action)
        if done:
            return info['total_reward'], info['total_profit']
#%%
class EvaluationCallback(BaseCallback):
    def __init__(self, data_length, eval_frequency_episodes, number_of_envs, number_of_days, eval_data, env_name, best_model_save_path, initial_best_profit = None, verbose=0):
        super(EvaluationCallback, self).__init__(verbose)
        self.data_length = data_length
        self.number_of_envs = number_of_envs
        self.number_of_days = number_of_days
        self.eval_frequency_episodes = eval_frequency_episodes
        self.eval_data = eval_data
        self.env_name = env_name
        self.best_model_save_path = best_model_save_path
        self.episode = 1
        
        if initial_best_profit is not None:
            self.best_avg_profit = initial_best_profit
            print(f"Initial best profit: {initial_best_profit}")
        else:
            self.best_avg_profit = None

    def _on_training_start(self) -> None:
        pass

    def _on_rollout_start(self) -> None:
        pass

    def _on_step(self) -> bool:
        episode = self.episode * self.number_of_envs
        
        if self.training_env.envs[0].current_tick == self.data_length - 2:
            self.episode += 1
            if episode % self.eval_frequency_episodes == 0:

                total_rewards = []
                total_profits = []

                for file, data in self.eval_data.items():
                    total_reward, total_profit = evaluate_model_using_data(self.env_name, data, self.number_of_days, self.model)
                    print(f"[Episode {episode}] Evaluation on file {file}. Total reward: {total_reward}, Total profit: {total_profit}")
                    total_rewards.append(total_reward)
                    total_profits.append(total_profit)

                avg_total_reward = np.mean(total_rewards)
                avg_total_profit = np.mean(total_profits)
                print(f'[Episode {episode}] Average total reward: {avg_total_reward}, Average total profit: {avg_total_profit}')
                self.logger.record("eval/avg_total_profit", avg_total_profit)
                self.logger.record("eval/avg_total_reward", avg_total_reward)

                if self.best_avg_profit is None:
                    print(f"[Episode {episode}] Saving new best model to {self.best_model_save_path}")
                    self.best_avg_profit = avg_total_profit
                    self.model.save(self.best_model_save_path)   
                elif avg_total_profit > self.best_avg_profit:
                    print(f"[Episode {episode}] Saving new best model to {self.best_model_save_path}")
                    self.best_avg_profit = avg_total_profit
                    self.model.save(self.best_model_save_path)
        
        return True

    def _on_rollout_end(self) -> None:
        pass

    def _on_training_end(self) -> None:
        pass
#%% md
# PPO, CONV V1
## 2 percent buckets 
## 3 day window size
## NN network with 1d convolutions
https://github.com/baosenguo/Kaggle-MoA-2nd-Place-Solution/blob/main/training/1d-cnn-train.ipynb
#%%
data = pd.read_csv('data/processed/PKO.WA_processed.csv', index_col='Date')
data.index = pd.to_datetime(data.index)
#%%
data = data.round(decimals=2)
#%%
data.replace([np.inf, -np.inf], 0, inplace=True)
#%%
data = data.fillna(0)
#%%
data_filtered = data[['Close', 'open_to_prev_1_open_pct_diff', 'high_to_prev_1_high_pct_diff', 'low_to_prev_1_low_pct_diff', 'close_to_prev_1_close_pct_diff', 'volume_to_prev_1_volume_pct_diff', 
                     'open_to_close_pct_diff', 'open_to_prev_1_close_pct_diff', 'open_to_low_pct_diff', 'open_to_prev_1_low_pct_diff', 'open_to_high_pct_diff', 
                      'open_to_prev_1_high_pct_diff', 'close_to_low_pct_diff', 'close_to_prev_1_low_pct_diff', 'close_to_high_pct_diff', 'close_to_prev_1_high_pct_diff', 
                      'low_to_high_pct_diff', 'low_to_prev_1_high_pct_diff', 
                     'roc7', 'roc30',
                     'day_of_week_1', 'day_of_week_2', 'day_of_week_3', 'day_of_week_4', 'day_of_week_5',
                     'open_to_prev_2_open_pct_diff', 'high_to_prev_2_high_pct_diff', 'low_to_prev_2_low_pct_diff', 'close_to_prev_2_close_pct_diff', 'volume_to_prev_2_volume_pct_diff',
                     'open_to_prev_2_close_pct_diff', 'open_to_prev_2_low_pct_diff', 'open_to_prev_2_high_pct_diff', 'close_to_prev_2_low_pct_diff', 'close_to_prev_2_high_pct_diff', 
                     'low_to_prev_2_high_pct_diff',
                     'open_to_prev_3_open_pct_diff', 'high_to_prev_3_high_pct_diff', 'low_to_prev_3_low_pct_diff', 'close_to_prev_3_close_pct_diff', 
                      'volume_to_prev_3_volume_pct_diff', 'open_to_prev_3_close_pct_diff', 'open_to_prev_3_low_pct_diff', 'open_to_prev_3_high_pct_diff', 
                      'close_to_prev_3_low_pct_diff', 'close_to_prev_3_high_pct_diff', 'low_to_prev_3_high_pct_diff']]
#%%
assign_to_bucket_columns = ['open_to_prev_1_open_pct_diff', 'high_to_prev_1_high_pct_diff', 'low_to_prev_1_low_pct_diff', 'close_to_prev_1_close_pct_diff', 'volume_to_prev_1_volume_pct_diff', 
                     'open_to_close_pct_diff', 'open_to_prev_1_close_pct_diff', 'open_to_low_pct_diff', 'open_to_prev_1_low_pct_diff', 'open_to_high_pct_diff', 
                      'open_to_prev_1_high_pct_diff', 'close_to_low_pct_diff', 'close_to_prev_1_low_pct_diff', 'close_to_high_pct_diff', 'close_to_prev_1_high_pct_diff', 
                      'low_to_high_pct_diff', 'low_to_prev_1_high_pct_diff', 
                     'roc7', 'roc30',
                     'open_to_prev_2_open_pct_diff', 'high_to_prev_2_high_pct_diff', 'low_to_prev_2_low_pct_diff', 'close_to_prev_2_close_pct_diff', 'volume_to_prev_2_volume_pct_diff',
                     'open_to_prev_2_close_pct_diff', 'open_to_prev_2_low_pct_diff', 'open_to_prev_2_high_pct_diff', 'close_to_prev_2_low_pct_diff', 'close_to_prev_2_high_pct_diff', 
                     'low_to_prev_2_high_pct_diff',
                      'open_to_prev_3_open_pct_diff', 'high_to_prev_3_high_pct_diff', 'low_to_prev_3_low_pct_diff', 'close_to_prev_3_close_pct_diff', 
                      'volume_to_prev_3_volume_pct_diff', 'open_to_prev_3_close_pct_diff', 'open_to_prev_3_low_pct_diff', 'open_to_prev_3_high_pct_diff', 
                      'close_to_prev_3_low_pct_diff', 'close_to_prev_3_high_pct_diff', 'low_to_prev_3_high_pct_diff']
#%%
data_filtered = convert_columns_to_buckets(assign_to_bucket_columns, data_filtered)
#%%
train_data = data_filtered['2005-01-01':'2020-01-01']
test_data = data_filtered['2020-01-01':]
#%%
tb_log_name = 'PPO_CONV_V1_PKO'
#%%
number_of_days = 3
#%%
number_of_envs = 10
#%%
seed = 100
#%%
env_name='single-stock-env-v3'
#%%
env = make_vec_env(env_name, n_envs=number_of_envs, env_kwargs=dict(data=train_data.values, window_size=number_of_days))
#%%
setup_seed(seed)
#%%
model = PPO(CustomActorCriticPolicy, env, verbose=0, tensorboard_log="./tensorboard", learning_rate=linear_schedule(0.0002), seed=seed)
#%%
model.policy
#%%
num_of_episodes = 180_000 // number_of_envs
eval_freq= 20
test_files = {
    'data/processed/PKO.WA_processed.csv': test_data
}

eval_callback = EvaluationCallback(eval_frequency_episodes=eval_freq,
                                          number_of_envs=number_of_envs,
                                          number_of_days=number_of_days,
                                          eval_data=test_files,
                                          env_name=env_name,
                                          best_model_save_path=f"rl_models/{tb_log_name}",
                                          initial_best_profit=None,
                                          data_length = len(train_data))

total_timesteps=(len(train_data)-number_of_days)*num_of_episodes-1
model.learn(total_timesteps=total_timesteps,
            tb_log_name=tb_log_name,
            callback=[
                CustomCallback(data_length = len(train_data), window_size=number_of_days, verbose=1, number_of_envs=number_of_envs, 
                               save_model_path=f"rl_models/{tb_log_name}_last", save_model_steps_frequency=len(train_data)*1000),
                eval_callback
            ])
#%%
model.save(f"rl_models/{tb_log_name}_last")
#%% md
# PPO, CONV V2
## 2 percent buckets 
## 3 day window size
## NN network with 1d convolutions
https://github.com/baosenguo/Kaggle-MoA-2nd-Place-Solution/blob/main/training/1d-cnn-train.ipynb
## removed dropout
#%%
class Conv1dModel(nn.Module):
        def __init__(self, num_features, num_targets, hidden_size):
            super(Conv1dModel, self).__init__()
            cha_1 = 128
            cha_2 = 256
            cha_3 = 256

            cha_1_reshape = int(hidden_size/cha_1)
            cha_po_1 = int(hidden_size/cha_1/2)
            cha_po_2 = int(hidden_size/cha_1/2/2) * cha_3

            self.cha_1 = cha_1
            self.cha_2 = cha_2
            self.cha_3 = cha_3
            self.cha_1_reshape = cha_1_reshape
            self.cha_po_1 = cha_po_1
            self.cha_po_2 = cha_po_2

            self.batch_norm1 = nn.BatchNorm1d(num_features)
            # self.dropout1 = nn.Dropout(0.05)
            self.dense1 = nn.utils.weight_norm(nn.Linear(num_features, hidden_size))

            self.batch_norm_c1 = nn.BatchNorm1d(cha_1)
            # self.dropout_c1 = nn.Dropout(0.05)
            self.conv1 = nn.utils.weight_norm(nn.Conv1d(cha_1,cha_2, kernel_size = 5, stride = 1, padding=2,  bias=False),dim=None)

            self.ave_po_c1 = nn.AdaptiveAvgPool1d(output_size = cha_po_1)

            self.batch_norm_c2 = nn.BatchNorm1d(cha_2)
            # self.dropout_c2 = nn.Dropout(0.05)
            self.conv2 = nn.utils.weight_norm(nn.Conv1d(cha_2,cha_2, kernel_size = 3, stride = 1, padding=1, bias=True),dim=None)

            self.batch_norm_c2_1 = nn.BatchNorm1d(cha_2)
            # self.dropout_c2_1 = nn.Dropout(0.15)
            self.conv2_1 = nn.utils.weight_norm(nn.Conv1d(cha_2,cha_2, kernel_size = 3, stride = 1, padding=1, bias=True),dim=None)

            self.batch_norm_c2_2 = nn.BatchNorm1d(cha_2)
            # self.dropout_c2_2 = nn.Dropout(0.1)
            self.conv2_2 = nn.utils.weight_norm(nn.Conv1d(cha_2,cha_3, kernel_size = 5, stride = 1, padding=2, bias=True),dim=None)

            self.max_po_c2 = nn.MaxPool1d(kernel_size=4, stride=2, padding=1)

            self.flt = nn.Flatten()

            self.batch_norm3 = nn.BatchNorm1d(cha_po_2)
            # self.dropout3 = nn.Dropout(0.1)
            self.dense3 = nn.utils.weight_norm(nn.Linear(cha_po_2, num_targets))

        def forward(self, x):

            x = self.batch_norm1(x)
            # x = self.dropout1(x)
            x = F.celu(self.dense1(x), alpha=0.06)

            x = x.reshape(x.shape[0],self.cha_1,
                          self.cha_1_reshape)

            x = self.batch_norm_c1(x)
            # x = self.dropout_c1(x)
            x = F.relu(self.conv1(x))

            x = self.ave_po_c1(x)

            x = self.batch_norm_c2(x)
            # x = self.dropout_c2(x)
            x = F.relu(self.conv2(x))
            x_s = x

            x = self.batch_norm_c2_1(x)
            # x = self.dropout_c2_1(x)
            x = F.relu(self.conv2_1(x))

            x = self.batch_norm_c2_2(x)
            # x = self.dropout_c2_2(x)
            x = F.relu(self.conv2_2(x))
            x =  x * x_s

            x = self.max_po_c2(x)

            x = self.flt(x)

            x = self.batch_norm3(x)
            # x = self.dropout3(x)
            x = self.dense3(x)

            return x
#%%
class CustomNetwork(nn.Module):

    def __init__(
        self,
        feature_dim: int
    ):
        super().__init__()

        # IMPORTANT:
        # Save output dimensions, used to create the distributions
        self.latent_dim_pi = 64
        self.latent_dim_vf = 64
        
        # Policy network
        self.policy_net = Conv1dModel(num_features=feature_dim, hidden_size=512, num_targets=64)
        # Value network
        self.value_net = Conv1dModel(num_features=feature_dim, hidden_size=512, num_targets=64)

    def forward(self, features: th.Tensor) -> Tuple[th.Tensor, th.Tensor]:
        return self.forward_actor(features), self.forward_critic(features)

    def forward_actor(self, features: th.Tensor) -> th.Tensor:
        return self.policy_net(features)

    def forward_critic(self, features: th.Tensor) -> th.Tensor:
        return self.value_net(features)
#%%
class CustomActorCriticPolicy(ActorCriticPolicy):
    def __init__(
        self,
        observation_space: spaces.Space,
        action_space: spaces.Space,
        lr_schedule: Callable[[float], float],
        *args,
        **kwargs,
    ): 
        super().__init__(
            observation_space,
            action_space,
            lr_schedule,
            # Pass remaining arguments to base class
            *args,
            **kwargs,
        )
        # Disable orthogonal initialization
        self.ortho_init = False

    def _build_mlp_extractor(self) -> None:
        self.mlp_extractor = CustomNetwork(self.features_dim)
#%%
data = pd.read_csv('data/processed/PKO.WA_processed.csv', index_col='Date')
data.index = pd.to_datetime(data.index)
#%%
data = data.round(decimals=2)
#%%
data.replace([np.inf, -np.inf], 0, inplace=True)
#%%
data = data.fillna(0)
#%%
data_filtered = data[['Close', 'open_to_prev_1_open_pct_diff', 'high_to_prev_1_high_pct_diff', 'low_to_prev_1_low_pct_diff', 'close_to_prev_1_close_pct_diff', 'volume_to_prev_1_volume_pct_diff', 
                     'open_to_close_pct_diff', 'open_to_prev_1_close_pct_diff', 'open_to_low_pct_diff', 'open_to_prev_1_low_pct_diff', 'open_to_high_pct_diff', 
                      'open_to_prev_1_high_pct_diff', 'close_to_low_pct_diff', 'close_to_prev_1_low_pct_diff', 'close_to_high_pct_diff', 'close_to_prev_1_high_pct_diff', 
                      'low_to_high_pct_diff', 'low_to_prev_1_high_pct_diff', 
                     'roc7', 'roc30',
                     'day_of_week_1', 'day_of_week_2', 'day_of_week_3', 'day_of_week_4', 'day_of_week_5',
                     'open_to_prev_2_open_pct_diff', 'high_to_prev_2_high_pct_diff', 'low_to_prev_2_low_pct_diff', 'close_to_prev_2_close_pct_diff', 'volume_to_prev_2_volume_pct_diff',
                     'open_to_prev_2_close_pct_diff', 'open_to_prev_2_low_pct_diff', 'open_to_prev_2_high_pct_diff', 'close_to_prev_2_low_pct_diff', 'close_to_prev_2_high_pct_diff', 
                     'low_to_prev_2_high_pct_diff',
                     'open_to_prev_3_open_pct_diff', 'high_to_prev_3_high_pct_diff', 'low_to_prev_3_low_pct_diff', 'close_to_prev_3_close_pct_diff', 
                      'volume_to_prev_3_volume_pct_diff', 'open_to_prev_3_close_pct_diff', 'open_to_prev_3_low_pct_diff', 'open_to_prev_3_high_pct_diff', 
                      'close_to_prev_3_low_pct_diff', 'close_to_prev_3_high_pct_diff', 'low_to_prev_3_high_pct_diff']]
#%%
assign_to_bucket_columns = ['open_to_prev_1_open_pct_diff', 'high_to_prev_1_high_pct_diff', 'low_to_prev_1_low_pct_diff', 'close_to_prev_1_close_pct_diff', 'volume_to_prev_1_volume_pct_diff', 
                     'open_to_close_pct_diff', 'open_to_prev_1_close_pct_diff', 'open_to_low_pct_diff', 'open_to_prev_1_low_pct_diff', 'open_to_high_pct_diff', 
                      'open_to_prev_1_high_pct_diff', 'close_to_low_pct_diff', 'close_to_prev_1_low_pct_diff', 'close_to_high_pct_diff', 'close_to_prev_1_high_pct_diff', 
                      'low_to_high_pct_diff', 'low_to_prev_1_high_pct_diff', 
                     'roc7', 'roc30',
                     'open_to_prev_2_open_pct_diff', 'high_to_prev_2_high_pct_diff', 'low_to_prev_2_low_pct_diff', 'close_to_prev_2_close_pct_diff', 'volume_to_prev_2_volume_pct_diff',
                     'open_to_prev_2_close_pct_diff', 'open_to_prev_2_low_pct_diff', 'open_to_prev_2_high_pct_diff', 'close_to_prev_2_low_pct_diff', 'close_to_prev_2_high_pct_diff', 
                     'low_to_prev_2_high_pct_diff',
                      'open_to_prev_3_open_pct_diff', 'high_to_prev_3_high_pct_diff', 'low_to_prev_3_low_pct_diff', 'close_to_prev_3_close_pct_diff', 
                      'volume_to_prev_3_volume_pct_diff', 'open_to_prev_3_close_pct_diff', 'open_to_prev_3_low_pct_diff', 'open_to_prev_3_high_pct_diff', 
                      'close_to_prev_3_low_pct_diff', 'close_to_prev_3_high_pct_diff', 'low_to_prev_3_high_pct_diff']
#%%
data_filtered = convert_columns_to_buckets(assign_to_bucket_columns, data_filtered)
#%%
train_data = data_filtered['2005-01-01':'2020-01-01']
test_data = data_filtered['2020-01-01':]
#%%
tb_log_name = 'PPO_CONV_V2_PKO'
#%%
number_of_days = 3
#%%
number_of_envs = 10
#%%
seed = 100
#%%
env_name='single-stock-env-v3'
#%%
env = make_vec_env(env_name, n_envs=number_of_envs, env_kwargs=dict(data=train_data.values, window_size=number_of_days))
#%%
setup_seed(seed)
#%%
model = PPO(CustomActorCriticPolicy, env, verbose=0, tensorboard_log="./tensorboard", learning_rate=linear_schedule(0.00015), seed=seed)
#%%
model.policy
#%%
num_of_episodes = 180_000 // number_of_envs
eval_freq= 20
test_files = {
    'data/processed/PKO.WA_processed.csv': test_data
}

eval_callback = EvaluationCallback(eval_frequency_episodes=eval_freq,
                                          number_of_envs=number_of_envs,
                                          number_of_days=number_of_days,
                                          eval_data=test_files,
                                          env_name=env_name,
                                          best_model_save_path=f"rl_models/{tb_log_name}",
                                          initial_best_profit=None,
                                          data_length = len(train_data))

total_timesteps=(len(train_data)-number_of_days)*num_of_episodes-1
model.learn(total_timesteps=total_timesteps,
            tb_log_name=tb_log_name,
            callback=[
                CustomCallback(data_length = len(train_data), window_size=number_of_days, verbose=1, number_of_envs=number_of_envs, 
                               save_model_path=f"rl_models/{tb_log_name}_last", save_model_steps_frequency=len(train_data)*1000),
                eval_callback
            ])
#%%
model.save(f"rl_models/{tb_log_name}_last")
#%% md
# PPO, CONV V3
## 2 percent buckets 
## 3 day window size
## smaller channels and network
#%%
class Conv1dModel(nn.Module):
        def __init__(self, num_features, num_targets, hidden_size):
            super(Conv1dModel, self).__init__()
            cha_1 = 64
            cha_2 = 128
            cha_3 = 128

            cha_1_reshape = int(hidden_size/cha_1)
            cha_po_1 = int(hidden_size/cha_1/2)
            cha_po_2 = int(hidden_size/cha_1/2/2) * cha_3

            self.cha_1 = cha_1
            self.cha_2 = cha_2
            self.cha_3 = cha_3
            self.cha_1_reshape = cha_1_reshape
            self.cha_po_1 = cha_po_1
            self.cha_po_2 = cha_po_2

            self.batch_norm1 = nn.BatchNorm1d(num_features)
            # self.dropout1 = nn.Dropout(0.05)
            self.dense1 = nn.utils.weight_norm(nn.Linear(num_features, hidden_size))

            self.batch_norm_c1 = nn.BatchNorm1d(cha_1)
            # self.dropout_c1 = nn.Dropout(0.05)
            self.conv1 = nn.utils.weight_norm(nn.Conv1d(cha_1,cha_2, kernel_size = 5, stride = 1, padding=2,  bias=False),dim=None)

            self.ave_po_c1 = nn.AdaptiveAvgPool1d(output_size = cha_po_1)

            self.batch_norm_c2 = nn.BatchNorm1d(cha_2)
            # self.dropout_c2 = nn.Dropout(0.05)
            self.conv2 = nn.utils.weight_norm(nn.Conv1d(cha_2,cha_2, kernel_size = 3, stride = 1, padding=1, bias=True),dim=None)

            self.batch_norm_c2_1 = nn.BatchNorm1d(cha_2)
            # self.dropout_c2_1 = nn.Dropout(0.15)
            self.conv2_1 = nn.utils.weight_norm(nn.Conv1d(cha_2,cha_2, kernel_size = 3, stride = 1, padding=1, bias=True),dim=None)

            self.batch_norm_c2_2 = nn.BatchNorm1d(cha_2)
            # self.dropout_c2_2 = nn.Dropout(0.1)
            self.conv2_2 = nn.utils.weight_norm(nn.Conv1d(cha_2,cha_3, kernel_size = 5, stride = 1, padding=2, bias=True),dim=None)

            self.max_po_c2 = nn.MaxPool1d(kernel_size=4, stride=2, padding=1)

            self.flt = nn.Flatten()

            self.batch_norm3 = nn.BatchNorm1d(cha_po_2)
            # self.dropout3 = nn.Dropout(0.1)
            self.dense3 = nn.utils.weight_norm(nn.Linear(cha_po_2, num_targets))

        def forward(self, x):

            x = self.batch_norm1(x)
            # x = self.dropout1(x)
            x = F.celu(self.dense1(x), alpha=0.06)

            x = x.reshape(x.shape[0],self.cha_1,
                          self.cha_1_reshape)

            x = self.batch_norm_c1(x)
            # x = self.dropout_c1(x)
            x = F.relu(self.conv1(x))

            x = self.ave_po_c1(x)

            x = self.batch_norm_c2(x)
            # x = self.dropout_c2(x)
            x = F.relu(self.conv2(x))
            x_s = x

            x = self.batch_norm_c2_1(x)
            # x = self.dropout_c2_1(x)
            x = F.relu(self.conv2_1(x))

            x = self.batch_norm_c2_2(x)
            # x = self.dropout_c2_2(x)
            x = F.relu(self.conv2_2(x))
            x =  x * x_s

            x = self.max_po_c2(x)

            x = self.flt(x)

            x = self.batch_norm3(x)
            # x = self.dropout3(x)
            x = self.dense3(x)

            return x
#%%
class CustomNetwork(nn.Module):

    def __init__(
        self,
        feature_dim: int
    ):
        super().__init__()

        # IMPORTANT:
        # Save output dimensions, used to create the distributions
        self.latent_dim_pi = 64
        self.latent_dim_vf = 64
        
        # Policy network
        self.policy_net = Conv1dModel(num_features=feature_dim, hidden_size=256, num_targets=64)
        # Value network
        self.value_net = Conv1dModel(num_features=feature_dim, hidden_size=256, num_targets=64)

    def forward(self, features: th.Tensor) -> Tuple[th.Tensor, th.Tensor]:
        return self.forward_actor(features), self.forward_critic(features)

    def forward_actor(self, features: th.Tensor) -> th.Tensor:
        return self.policy_net(features)

    def forward_critic(self, features: th.Tensor) -> th.Tensor:
        return self.value_net(features)
#%%
class CustomActorCriticPolicy(ActorCriticPolicy):
    def __init__(
        self,
        observation_space: spaces.Space,
        action_space: spaces.Space,
        lr_schedule: Callable[[float], float],
        *args,
        **kwargs,
    ): 
        super().__init__(
            observation_space,
            action_space,
            lr_schedule,
            # Pass remaining arguments to base class
            *args,
            **kwargs,
        )
        # Disable orthogonal initialization
        self.ortho_init = False

    def _build_mlp_extractor(self) -> None:
        self.mlp_extractor = CustomNetwork(self.features_dim)
#%%
data = pd.read_csv('data/processed/PKO.WA_processed.csv', index_col='Date')
data.index = pd.to_datetime(data.index)
#%%
data = data.round(decimals=2)
#%%
data.replace([np.inf, -np.inf], 0, inplace=True)
#%%
data = data.fillna(0)
#%%
data_filtered = data[['Close', 'open_to_prev_1_open_pct_diff', 'high_to_prev_1_high_pct_diff', 'low_to_prev_1_low_pct_diff', 'close_to_prev_1_close_pct_diff', 'volume_to_prev_1_volume_pct_diff', 
                     'open_to_close_pct_diff', 'open_to_prev_1_close_pct_diff', 'open_to_low_pct_diff', 'open_to_prev_1_low_pct_diff', 'open_to_high_pct_diff', 
                      'open_to_prev_1_high_pct_diff', 'close_to_low_pct_diff', 'close_to_prev_1_low_pct_diff', 'close_to_high_pct_diff', 'close_to_prev_1_high_pct_diff', 
                      'low_to_high_pct_diff', 'low_to_prev_1_high_pct_diff', 
                     'roc7', 'roc30',
                     'day_of_week_1', 'day_of_week_2', 'day_of_week_3', 'day_of_week_4', 'day_of_week_5',
                     'open_to_prev_2_open_pct_diff', 'high_to_prev_2_high_pct_diff', 'low_to_prev_2_low_pct_diff', 'close_to_prev_2_close_pct_diff', 'volume_to_prev_2_volume_pct_diff',
                     'open_to_prev_2_close_pct_diff', 'open_to_prev_2_low_pct_diff', 'open_to_prev_2_high_pct_diff', 'close_to_prev_2_low_pct_diff', 'close_to_prev_2_high_pct_diff', 
                     'low_to_prev_2_high_pct_diff',
                     'open_to_prev_3_open_pct_diff', 'high_to_prev_3_high_pct_diff', 'low_to_prev_3_low_pct_diff', 'close_to_prev_3_close_pct_diff', 
                      'volume_to_prev_3_volume_pct_diff', 'open_to_prev_3_close_pct_diff', 'open_to_prev_3_low_pct_diff', 'open_to_prev_3_high_pct_diff', 
                      'close_to_prev_3_low_pct_diff', 'close_to_prev_3_high_pct_diff', 'low_to_prev_3_high_pct_diff']]
#%%
assign_to_bucket_columns = ['open_to_prev_1_open_pct_diff', 'high_to_prev_1_high_pct_diff', 'low_to_prev_1_low_pct_diff', 'close_to_prev_1_close_pct_diff', 'volume_to_prev_1_volume_pct_diff', 
                     'open_to_close_pct_diff', 'open_to_prev_1_close_pct_diff', 'open_to_low_pct_diff', 'open_to_prev_1_low_pct_diff', 'open_to_high_pct_diff', 
                      'open_to_prev_1_high_pct_diff', 'close_to_low_pct_diff', 'close_to_prev_1_low_pct_diff', 'close_to_high_pct_diff', 'close_to_prev_1_high_pct_diff', 
                      'low_to_high_pct_diff', 'low_to_prev_1_high_pct_diff', 
                     'roc7', 'roc30',
                     'open_to_prev_2_open_pct_diff', 'high_to_prev_2_high_pct_diff', 'low_to_prev_2_low_pct_diff', 'close_to_prev_2_close_pct_diff', 'volume_to_prev_2_volume_pct_diff',
                     'open_to_prev_2_close_pct_diff', 'open_to_prev_2_low_pct_diff', 'open_to_prev_2_high_pct_diff', 'close_to_prev_2_low_pct_diff', 'close_to_prev_2_high_pct_diff', 
                     'low_to_prev_2_high_pct_diff',
                      'open_to_prev_3_open_pct_diff', 'high_to_prev_3_high_pct_diff', 'low_to_prev_3_low_pct_diff', 'close_to_prev_3_close_pct_diff', 
                      'volume_to_prev_3_volume_pct_diff', 'open_to_prev_3_close_pct_diff', 'open_to_prev_3_low_pct_diff', 'open_to_prev_3_high_pct_diff', 
                      'close_to_prev_3_low_pct_diff', 'close_to_prev_3_high_pct_diff', 'low_to_prev_3_high_pct_diff']
#%%
data_filtered = convert_columns_to_buckets(assign_to_bucket_columns, data_filtered)
#%%
train_data = data_filtered['2005-01-01':'2020-01-01']
test_data = data_filtered['2020-01-01':]
#%%
tb_log_name = 'PPO_CONV_V3_PKO'
#%%
number_of_days = 3
#%%
number_of_envs = 10
#%%
seed = 100
#%%
env_name='single-stock-env-v3'
#%%
env = make_vec_env(env_name, n_envs=number_of_envs, env_kwargs=dict(data=train_data.values, window_size=number_of_days))
#%%
setup_seed(seed)
#%%
model = PPO(CustomActorCriticPolicy, env, verbose=0, tensorboard_log="./tensorboard", learning_rate=linear_schedule(0.00015), seed=seed)
#%%
model.policy
#%%
num_of_episodes = 180_000 // number_of_envs
eval_freq= 20
test_files = {
    'data/processed/PKO.WA_processed.csv': test_data
}

eval_callback = EvaluationCallback(eval_frequency_episodes=eval_freq,
                                          number_of_envs=number_of_envs,
                                          number_of_days=number_of_days,
                                          eval_data=test_files,
                                          env_name=env_name,
                                          best_model_save_path=f"rl_models/{tb_log_name}",
                                          initial_best_profit=None,
                                          data_length = len(train_data))

total_timesteps=(len(train_data)-number_of_days)*num_of_episodes-1
model.learn(total_timesteps=total_timesteps,
            tb_log_name=tb_log_name,
            callback=[
                CustomCallback(data_length = len(train_data), window_size=number_of_days, verbose=1, number_of_envs=number_of_envs, 
                               save_model_path=f"rl_models/{tb_log_name}_last", save_model_steps_frequency=len(train_data)*1000),
                eval_callback
            ])
#%%
model.save(f"rl_models/{tb_log_name}_last")
#%%
