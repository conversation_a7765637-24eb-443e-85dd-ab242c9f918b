#%%
import pandas as pd
import numpy as np

import torch as th
import torch.nn as nn

from gym import spaces

from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.vec_env import VecFrameStack
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor
from stable_baselines3 import PPO

from speculator.rl.utils.train_utils import load_data_from_files, setup_seed, linear_schedule
from speculator.rl.utils.sb3.evaluation_callback_v2 import EvaluationCallbackV2
from speculator.rl.utils.sb3.custom_callback_v2 import CustomCallbackV2
from speculator.rl.environment import StockImgEnvV4
from speculator.rl.model.cnn_network import CnnNetwork
#%% md
## PPO trained on images, V9
#%%
tb_log_name = 'PPO_CNN_PKO_V9'
number_of_days = 6

policy_kwargs = dict(
    features_extractor_class=CnnNetwork,
    features_extractor_kwargs=dict(
        kernel_size = 5,
        cnn_architecture = [8, 8, 'M', 16, 16, 'M', 32],
        linear_architecture = ['D', 256]
    ),
)

lr = 0.00003
number_of_envs = 10
seed = 100
env_name='stock-img-env-v4'
#%%
columns = ['Open', 'High', 'Low', 'Close']
assign_to_bucket_columns = []
files = [
    'data/processed/PKO.WA_processed.csv'
]

train_files, test_files = load_data_from_files(files, '1960-01-01', '2020-01-01', columns, assign_to_bucket_columns)
train_data = [d for d in train_files.values()]
test_data = [d for d in test_files.values()]

env = make_vec_env(env_name, n_envs=number_of_envs, 
                   env_kwargs=dict(
                       data=train_data, 
                       window_size=number_of_days, 
                       change_data_frequency=2000,
                       figsize=(1.9, 1.9), 
                       dpi=50,
                       grayscale=True
                   )
                  )

env = VecFrameStack(env, n_stack=3)
setup_seed(seed)

model = PPO('CnnPolicy', env, verbose=0, tensorboard_log="./tensorboard", learning_rate=linear_schedule(lr), seed=seed, policy_kwargs=policy_kwargs)
print(model.policy)
#%%
num_of_episodes = 300_000 // number_of_envs
all_data_length = sum([len(d) for d in train_data])

callback = CustomCallbackV2(number_of_days=number_of_days,
                            eval_data=test_data,
                            env_name=env_name,
                            eval_model_episodes_frequency=5,
                            best_model_save_path=f"rl_models/{tb_log_name}",
                            save_model_episodes_frequency=10,
                            save_model_path=f"rl_models/{tb_log_name}_last",
                            n_frame_stack=3,
                            figsize=(1.9, 1.9), 
                            dpi=50,
                            grayscale=True
                            )

total_timesteps=(all_data_length-(number_of_days*len(train_data)))*num_of_episodes-1
model.learn(total_timesteps=total_timesteps,
            tb_log_name=tb_log_name,
            callback=callback)
#%% md
## PPO trained on images, V9.1
#%%
tb_log_name = 'PPO_CNN_PKO_V9.1'
number_of_days = 5

policy_kwargs = dict(
    features_extractor_class=CnnNetwork,
    features_extractor_kwargs=dict(
        kernel_size = 5,
        cnn_architecture = [8, 8, 'M', 16, 16, 'M', 32],
        linear_architecture = ['D', 256]
    ),
)

lr = 0.00003
number_of_envs = 10
seed = 100
env_name='stock-img-env-v4'
#%%
columns = ['Open', 'High', 'Low', 'Close']
assign_to_bucket_columns = []
files = [
    'data/processed/PKO.WA_processed.csv'
]

train_files, test_files = load_data_from_files(files, '1960-01-01', '2020-01-01', columns, assign_to_bucket_columns)
train_data = [d for d in train_files.values()]
test_data = [d for d in test_files.values()]

env = make_vec_env(env_name, n_envs=number_of_envs, 
                   env_kwargs=dict(
                       data=train_data, 
                       window_size=number_of_days, 
                       change_data_frequency=2000,
                       figsize=(1.9, 1.9), 
                       dpi=50,
                       grayscale=True
                   )
                  )

env = VecFrameStack(env, n_stack=3)
setup_seed(seed)

model = PPO('CnnPolicy', env, verbose=0, tensorboard_log="./tensorboard", learning_rate=linear_schedule(lr), seed=seed, policy_kwargs=policy_kwargs)
print(model.policy)
#%%
num_of_episodes = 300_000 // number_of_envs
all_data_length = sum([len(d) for d in train_data])

callback = CustomCallbackV2(number_of_days=number_of_days,
                            eval_data=test_data,
                            env_name=env_name,
                            eval_model_episodes_frequency=5,
                            best_model_save_path=f"rl_models/{tb_log_name}",
                            save_model_episodes_frequency=10,
                            save_model_path=f"rl_models/{tb_log_name}_last",
                            n_frame_stack=3,
                            figsize=(1.9, 1.9), 
                            dpi=50,
                            grayscale=True
                            )

total_timesteps=(all_data_length-(number_of_days*len(train_data)))*num_of_episodes-1
model.learn(total_timesteps=total_timesteps,
            tb_log_name=tb_log_name,
            callback=callback)
#%% md
## PPO trained on images, V9.2
#%%
tb_log_name = 'PPO_CNN_PKO_V9.2'
number_of_days = 4

policy_kwargs = dict(
    features_extractor_class=CnnNetwork,
    features_extractor_kwargs=dict(
        kernel_size = 5,
        cnn_architecture = [8, 8, 'M', 16, 16, 'M', 32],
        linear_architecture = ['D', 256]
    ),
)

lr = 0.00003
number_of_envs = 10
seed = 100
env_name='stock-img-env-v4'
#%%
columns = ['Open', 'High', 'Low', 'Close']
assign_to_bucket_columns = []
files = [
    'data/processed/PKO.WA_processed.csv'
]

train_files, test_files = load_data_from_files(files, '1960-01-01', '2020-01-01', columns, assign_to_bucket_columns)
train_data = [d for d in train_files.values()]
test_data = [d for d in test_files.values()]

env = make_vec_env(env_name, n_envs=number_of_envs, 
                   env_kwargs=dict(
                       data=train_data, 
                       window_size=number_of_days, 
                       change_data_frequency=2000,
                       figsize=(1.9, 1.9), 
                       dpi=50,
                       grayscale=True
                   )
                  )

env = VecFrameStack(env, n_stack=3)
setup_seed(seed)

model = PPO('CnnPolicy', env, verbose=0, tensorboard_log="./tensorboard", learning_rate=linear_schedule(lr), seed=seed, policy_kwargs=policy_kwargs)
print(model.policy)
#%%
num_of_episodes = 300_000 // number_of_envs
all_data_length = sum([len(d) for d in train_data])

callback = CustomCallbackV2(number_of_days=number_of_days,
                            eval_data=test_data,
                            env_name=env_name,
                            eval_model_episodes_frequency=5,
                            best_model_save_path=f"rl_models/{tb_log_name}",
                            save_model_episodes_frequency=10,
                            save_model_path=f"rl_models/{tb_log_name}_last",
                            n_frame_stack=3,
                            figsize=(1.9, 1.9), 
                            dpi=50,
                            grayscale=True
                            )

total_timesteps=(all_data_length-(number_of_days*len(train_data)))*num_of_episodes-1
model.learn(total_timesteps=total_timesteps,
            tb_log_name=tb_log_name,
            callback=callback)
#%% md
## PPO trained on images, V9.3
#%%
tb_log_name = 'PPO_CNN_PKO_V9.3'
number_of_days = 3

policy_kwargs = dict(
    features_extractor_class=CnnNetwork,
    features_extractor_kwargs=dict(
        kernel_size = 5,
        cnn_architecture = [8, 8, 'M', 16, 16, 'M', 32],
        linear_architecture = ['D', 256]
    ),
)

lr = 0.00003
number_of_envs = 10
seed = 100
env_name='stock-img-env-v4'
#%%
columns = ['Open', 'High', 'Low', 'Close']
assign_to_bucket_columns = []
files = [
    'data/processed/PKO.WA_processed.csv'
]

train_files, test_files = load_data_from_files(files, '1960-01-01', '2020-01-01', columns, assign_to_bucket_columns)
train_data = [d for d in train_files.values()]
test_data = [d for d in test_files.values()]

env = make_vec_env(env_name, n_envs=number_of_envs, 
                   env_kwargs=dict(
                       data=train_data, 
                       window_size=number_of_days, 
                       change_data_frequency=2000,
                       figsize=(1.9, 1.9), 
                       dpi=50,
                       grayscale=True
                   )
                  )

env = VecFrameStack(env, n_stack=3)
setup_seed(seed)

model = PPO('CnnPolicy', env, verbose=0, tensorboard_log="./tensorboard", learning_rate=linear_schedule(lr), seed=seed, policy_kwargs=policy_kwargs)
print(model.policy)
#%%
num_of_episodes = 300_000 // number_of_envs
all_data_length = sum([len(d) for d in train_data])

callback = CustomCallbackV2(number_of_days=number_of_days,
                            eval_data=test_data,
                            env_name=env_name,
                            eval_model_episodes_frequency=5,
                            best_model_save_path=f"rl_models/{tb_log_name}",
                            save_model_episodes_frequency=10,
                            save_model_path=f"rl_models/{tb_log_name}_last",
                            n_frame_stack=3,
                            figsize=(1.9, 1.9), 
                            dpi=50,
                            grayscale=True
                            )

total_timesteps=(all_data_length-(number_of_days*len(train_data)))*num_of_episodes-1
model.learn(total_timesteps=total_timesteps,
            tb_log_name=tb_log_name,
            callback=callback)
#%% md
## PPO trained on images, V9.4
#%%
tb_log_name = 'PPO_CNN_PKO_V9.4'
number_of_days = 2

policy_kwargs = dict(
    features_extractor_class=CnnNetwork,
    features_extractor_kwargs=dict(
        kernel_size = 5,
        cnn_architecture = [8, 8, 'M', 16, 16, 'M', 32],
        linear_architecture = ['D', 256]
    ),
)

lr = 0.00003
number_of_envs = 10
seed = 100
env_name='stock-img-env-v4'
#%%
columns = ['Open', 'High', 'Low', 'Close']
assign_to_bucket_columns = []
files = [
    'data/processed/PKO.WA_processed.csv'
]

train_files, test_files = load_data_from_files(files, '1960-01-01', '2020-01-01', columns, assign_to_bucket_columns)
train_data = [d for d in train_files.values()]
test_data = [d for d in test_files.values()]

env = make_vec_env(env_name, n_envs=number_of_envs, 
                   env_kwargs=dict(
                       data=train_data, 
                       window_size=number_of_days, 
                       change_data_frequency=2000,
                       figsize=(1.9, 1.9), 
                       dpi=50,
                       grayscale=True
                   )
                  )

env = VecFrameStack(env, n_stack=3)
setup_seed(seed)

model = PPO('CnnPolicy', env, verbose=0, tensorboard_log="./tensorboard", learning_rate=linear_schedule(lr), seed=seed, policy_kwargs=policy_kwargs)
print(model.policy)
#%%
num_of_episodes = 300_000 // number_of_envs
all_data_length = sum([len(d) for d in train_data])

callback = CustomCallbackV2(number_of_days=number_of_days,
                            eval_data=test_data,
                            env_name=env_name,
                            eval_model_episodes_frequency=5,
                            best_model_save_path=f"rl_models/{tb_log_name}",
                            save_model_episodes_frequency=10,
                            save_model_path=f"rl_models/{tb_log_name}_last",
                            n_frame_stack=3,
                            figsize=(1.9, 1.9), 
                            dpi=50,
                            grayscale=True
                            )

total_timesteps=(all_data_length-(number_of_days*len(train_data)))*num_of_episodes-1
model.learn(total_timesteps=total_timesteps,
            tb_log_name=tb_log_name,
            callback=callback)
#%% md
## PPO trained on images, V9.5
#%%
tb_log_name = 'PPO_CNN_PKO_V9.5'
number_of_days = 7

policy_kwargs = dict(
    features_extractor_class=CnnNetwork,
    features_extractor_kwargs=dict(
        kernel_size = 5,
        cnn_architecture = [8, 8, 'M', 16, 16, 'M', 32],
        linear_architecture = ['D', 256]
    ),
)

lr = 0.00003
number_of_envs = 10
seed = 100
env_name='stock-img-env-v4'
#%%
columns = ['Open', 'High', 'Low', 'Close']
assign_to_bucket_columns = []
files = [
    'data/processed/PKO.WA_processed.csv'
]

train_files, test_files = load_data_from_files(files, '1960-01-01', '2020-01-01', columns, assign_to_bucket_columns)
train_data = [d for d in train_files.values()]
test_data = [d for d in test_files.values()]

env = make_vec_env(env_name, n_envs=number_of_envs, 
                   env_kwargs=dict(
                       data=train_data, 
                       window_size=number_of_days, 
                       change_data_frequency=2000,
                       figsize=(1.9, 1.9), 
                       dpi=50,
                       grayscale=True
                   )
                  )

env = VecFrameStack(env, n_stack=3)
setup_seed(seed)

model = PPO('CnnPolicy', env, verbose=0, tensorboard_log="./tensorboard", learning_rate=linear_schedule(lr), seed=seed, policy_kwargs=policy_kwargs)
print(model.policy)
#%%
num_of_episodes = 300_000 // number_of_envs
all_data_length = sum([len(d) for d in train_data])

callback = CustomCallbackV2(number_of_days=number_of_days,
                            eval_data=test_data,
                            env_name=env_name,
                            eval_model_episodes_frequency=5,
                            best_model_save_path=f"rl_models/{tb_log_name}",
                            save_model_episodes_frequency=10,
                            save_model_path=f"rl_models/{tb_log_name}_last",
                            n_frame_stack=3,
                            figsize=(1.9, 1.9), 
                            dpi=50,
                            grayscale=True
                            )

total_timesteps=(all_data_length-(number_of_days*len(train_data)))*num_of_episodes-1
model.learn(total_timesteps=total_timesteps,
            tb_log_name=tb_log_name,
            callback=callback)
#%%
