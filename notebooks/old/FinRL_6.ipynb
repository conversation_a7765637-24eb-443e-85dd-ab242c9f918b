#%%
from speculator.rl.utils.train_utils import load_data_from_files, setup_seed, linear_schedule
from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv
from stable_baselines3 import TD3
from stable_baselines3.common.vec_env import DummyVecEnv
from speculator.rl.utils.sb3.custom_callback_finrl import CustomCallbackFinRL

import pandas as pd
import itertools
#%%
def process_data(df, ticker):
    df.reset_index(inplace=True)
    df['tic'] = ticker
    df = df.rename(columns={"Close": "close", 'Date': 'date'})
    return df
#%%
def prepare_data(processed):
    list_ticker = processed["tic"].unique().tolist()
    list_date = list(pd.date_range(processed['date'].min(),processed['date'].max()).astype(str))
    combination = list(itertools.product(list_date,list_ticker))
    processed_full = pd.DataFrame(combination,columns=["date","tic"])
    processed_full['date'] = processed_full['date'].astype('datetime64[ns]')
    processed_full = processed_full.merge(processed,on=["date","tic"],how="left")
    processed_full = processed_full[processed_full['date'].isin(processed['date'])]
    processed_full = processed_full.sort_values(['date','tic'])
    processed_full = processed_full.fillna(0)
    
    stock_dimension = len(processed_full.tic.unique())
    state_space = 1 + 2*stock_dimension + len(assign_to_bucket_columns)*stock_dimension
    print(f"Stock Dimension: {stock_dimension}, State Space: {state_space}")
    
    processed_full = processed_full.sort_values(['date', "tic"], ignore_index=True)
    processed_full.index = processed_full['date'].factorize()[0]
    return processed_full, stock_dimension, state_space
#%% md
# FinRL env v1
## TD3
#%%
TRAIN_START_DATE = '2005-01-01'
TRAIN_END_DATE = '2020-01-02'
TRADE_START_DATE = '2020-01-02'
TRADE_END_DATE = '2023-01-01'

tb_log_name = 'FIN_TD3_RL_IBM_IEF_MO_V1'
number_of_days = 1
number_of_envs = 10
seed = 100
#%%
columns = ['Close', 'open_close_to_low_high_ratio', 'prev_1_open_close_to_low_high_ratio',
                      'gap', 'prev_1_gap',
                      'open_to_close_ratio', 'prev_1_open_to_close_ratio', 'prev_2_open_to_close_ratio',
                      'close_to_prev_1_open_ratio', 
                      'open_to_prev_1_close_ratio', 'prev_1_open_to_prev_1_close_ratio',
                      'open_to_prev_1_open_ratio', 
                      'close_to_prev_1_close_ratio', 
                      'open_to_prev_2_open_ratio', 
                      'close_to_prev_2_close_ratio',
                      'close_high_to_low_high_ratio', 'prev_1_close_high_to_low_high_ratio', 
                      'open_high_to_low_high_ratio',
                      'close_to_prev_1_open_close_midpoint_ratio', 
                      'open_to_prev_1_high_ratio',
                      'close_low_to_low_high_ratio', 'prev_1_close_low_to_low_high_ratio',
                      'open_low_to_low_high_ratio', 'prev_1_open_low_to_low_high_ratio',
                      'open_close_diff_to_prev_1_open_close_diff_ratio', 'prev_1_open_close_diff_to_prev_1_open_close_diff_ratio',
                      'high_low_diff_to_prev_1_high_low_diff_ratio', 'prev_1_high_low_diff_to_prev_1_high_low_diff_ratio',
                      'close_to_prev_2_open_close_midpoint_ratio', 
                      'close_to_sma50_ratio', 'prev_1_close_to_sma50_ratio',
                      'close_to_sma200_ratio', 'prev_1_close_to_sma200_ratio',
                      'sma50_to_sma200_ratio', 'prev_1_sma50_to_sma200_ratio',
                      'volume_to_prev_1_volume_ratio', 'stoch_fast_k', 'stoch_fast_d',
                       'close_to_st_5_2', 'prev_1_close_to_st_5_2', 'prev_2_close_to_st_5_2'
          ]
assign_to_bucket_columns = ['open_close_to_low_high_ratio', 'prev_1_open_close_to_low_high_ratio',
                      'gap', 'prev_1_gap',
                      'open_to_close_ratio', 'prev_1_open_to_close_ratio', 'prev_2_open_to_close_ratio',
                      'close_to_prev_1_open_ratio', 
                      'open_to_prev_1_close_ratio', 'prev_1_open_to_prev_1_close_ratio',
                      'open_to_prev_1_open_ratio', 
                      'close_to_prev_1_close_ratio', 
                      'open_to_prev_2_open_ratio', 
                      'close_to_prev_2_close_ratio',
                      'close_high_to_low_high_ratio', 'prev_1_close_high_to_low_high_ratio', 
                      'open_high_to_low_high_ratio',
                      'close_to_prev_1_open_close_midpoint_ratio', 
                      'open_to_prev_1_high_ratio',
                      'close_low_to_low_high_ratio', 'prev_1_close_low_to_low_high_ratio',
                      'open_low_to_low_high_ratio', 'prev_1_open_low_to_low_high_ratio',
                      'open_close_diff_to_prev_1_open_close_diff_ratio', 'prev_1_open_close_diff_to_prev_1_open_close_diff_ratio',
                      'high_low_diff_to_prev_1_high_low_diff_ratio', 'prev_1_high_low_diff_to_prev_1_high_low_diff_ratio',
                      'close_to_prev_2_open_close_midpoint_ratio', 
                      'close_to_sma50_ratio', 'prev_1_close_to_sma50_ratio',
                      'close_to_sma200_ratio', 'prev_1_close_to_sma200_ratio',
                      'sma50_to_sma200_ratio', 'prev_1_sma50_to_sma200_ratio',
                      'volume_to_prev_1_volume_ratio', 'stoch_fast_k', 'stoch_fast_d',
                      'close_to_st_5_2', 'prev_1_close_to_st_5_2', 'prev_2_close_to_st_5_2']
#%%
files = [
    'data/processed/IBM_processed.csv',
    'data/processed/IEF_processed.csv',
    'data/processed/MO_processed.csv',
]
#%%
train_files, test_files = load_data_from_files(files, TRAIN_START_DATE, TRADE_END_DATE, columns, assign_to_bucket_columns, bucket_granularity=2)
#%%
processed_ibm = process_data(train_files['data/processed/IBM_processed.csv'], 'IBM')
processed_ief = process_data(train_files['data/processed/IEF_processed.csv'], 'IEF')
processed_mo = process_data(train_files['data/processed/MO_processed.csv'], 'MO')
processed = pd.concat([processed_ibm, processed_ief, processed_mo])

processed_full, stock_dimension, state_space = prepare_data(processed)
#%%
setup_seed(seed)
#%%
buy_cost_list = sell_cost_list = [0.001] * stock_dimension
num_stock_shares = [0] * stock_dimension

env_kwargs = {
    "hmax": 100,
    "initial_amount": 1000000,
    "num_stock_shares": num_stock_shares,
    "buy_cost_pct": buy_cost_list,
    "sell_cost_pct": sell_cost_list,
    "state_space": state_space,
    "stock_dim": stock_dimension,
    "tech_indicator_list": assign_to_bucket_columns,
    "action_space": stock_dimension,
    "reward_scaling": 1e-4
}

e_train_gym = StockTradingEnv(df = processed_full, **env_kwargs)
#%%
env = DummyVecEnv([lambda: e_train_gym])
#%%
model = TD3('MlpPolicy', env, verbose=0, tensorboard_log="./FinRL/tensorboard", learning_rate=linear_schedule(0.0001), seed=seed)
#%%
model.policy
#%%
test_ibm = process_data(test_files['data/processed/IBM_processed.csv'], 'IBM')
test_ief = process_data(test_files['data/processed/IEF_processed.csv'], 'IEF')
test_mo = process_data(test_files['data/processed/MO_processed.csv'], 'MO')
test_processed = pd.concat([test_ibm, test_ief, test_mo])

test_processed_full, _, _ = prepare_data(test_processed)
#%%
steps_in_episode = processed_full.shape[0] // len(train_files)
num_of_episodes = 1000
total_steps = num_of_episodes * steps_in_episode

callback = CustomCallbackFinRL(
                            eval_data=test_processed_full,
                            env_kwargs=env_kwargs,
                            eval_model_episodes_frequency=5,
                            best_model_save_path=f"FinRL/rl_models/{tb_log_name}",
                            save_model_episodes_frequency=10,
                            save_model_path=f"FinRL/rl_models/{tb_log_name}_last"
                            )

model.learn(total_timesteps=total_steps,
            tb_log_name=tb_log_name,
            callback=callback)
#%% md
# FinRL env v2
## TD3
#%%
TRAIN_START_DATE = '2005-01-01'
TRAIN_END_DATE = '2020-01-02'
TRADE_START_DATE = '2020-01-02'
TRADE_END_DATE = '2023-01-01'

tb_log_name = 'FIN_TD3_RL_IBM_IEF_MO_V2'
number_of_days = 1
number_of_envs = 10
seed = 100
policy_kwargs = dict(net_arch=[128, 256, 512, 256, 128])
#%%
columns = ['Close', 'open_close_to_low_high_ratio', 'prev_1_open_close_to_low_high_ratio',
                      'gap', 'prev_1_gap',
                      'open_to_close_ratio', 'prev_1_open_to_close_ratio', 'prev_2_open_to_close_ratio',
                      'close_to_prev_1_open_ratio', 
                      'open_to_prev_1_close_ratio', 'prev_1_open_to_prev_1_close_ratio',
                      'open_to_prev_1_open_ratio', 
                      'close_to_prev_1_close_ratio', 
                      'open_to_prev_2_open_ratio', 
                      'close_to_prev_2_close_ratio',
                      'close_high_to_low_high_ratio', 'prev_1_close_high_to_low_high_ratio', 
                      'open_high_to_low_high_ratio',
                      'close_to_prev_1_open_close_midpoint_ratio', 
                      'open_to_prev_1_high_ratio',
                      'close_low_to_low_high_ratio', 'prev_1_close_low_to_low_high_ratio',
                      'open_low_to_low_high_ratio', 'prev_1_open_low_to_low_high_ratio',
                      'open_close_diff_to_prev_1_open_close_diff_ratio', 'prev_1_open_close_diff_to_prev_1_open_close_diff_ratio',
                      'high_low_diff_to_prev_1_high_low_diff_ratio', 'prev_1_high_low_diff_to_prev_1_high_low_diff_ratio',
                      'close_to_prev_2_open_close_midpoint_ratio', 
                      'close_to_sma50_ratio', 'prev_1_close_to_sma50_ratio',
                      'close_to_sma200_ratio', 'prev_1_close_to_sma200_ratio',
                      'sma50_to_sma200_ratio', 'prev_1_sma50_to_sma200_ratio',
                      'volume_to_prev_1_volume_ratio', 'stoch_fast_k', 'stoch_fast_d',
                       'close_to_st_5_2', 'prev_1_close_to_st_5_2', 'prev_2_close_to_st_5_2'
          ]
assign_to_bucket_columns = ['open_close_to_low_high_ratio', 'prev_1_open_close_to_low_high_ratio',
                      'gap', 'prev_1_gap',
                      'open_to_close_ratio', 'prev_1_open_to_close_ratio', 'prev_2_open_to_close_ratio',
                      'close_to_prev_1_open_ratio', 
                      'open_to_prev_1_close_ratio', 'prev_1_open_to_prev_1_close_ratio',
                      'open_to_prev_1_open_ratio', 
                      'close_to_prev_1_close_ratio', 
                      'open_to_prev_2_open_ratio', 
                      'close_to_prev_2_close_ratio',
                      'close_high_to_low_high_ratio', 'prev_1_close_high_to_low_high_ratio', 
                      'open_high_to_low_high_ratio',
                      'close_to_prev_1_open_close_midpoint_ratio', 
                      'open_to_prev_1_high_ratio',
                      'close_low_to_low_high_ratio', 'prev_1_close_low_to_low_high_ratio',
                      'open_low_to_low_high_ratio', 'prev_1_open_low_to_low_high_ratio',
                      'open_close_diff_to_prev_1_open_close_diff_ratio', 'prev_1_open_close_diff_to_prev_1_open_close_diff_ratio',
                      'high_low_diff_to_prev_1_high_low_diff_ratio', 'prev_1_high_low_diff_to_prev_1_high_low_diff_ratio',
                      'close_to_prev_2_open_close_midpoint_ratio', 
                      'close_to_sma50_ratio', 'prev_1_close_to_sma50_ratio',
                      'close_to_sma200_ratio', 'prev_1_close_to_sma200_ratio',
                      'sma50_to_sma200_ratio', 'prev_1_sma50_to_sma200_ratio',
                      'volume_to_prev_1_volume_ratio', 'stoch_fast_k', 'stoch_fast_d',
                      'close_to_st_5_2', 'prev_1_close_to_st_5_2', 'prev_2_close_to_st_5_2']
#%%
files = [
    'data/processed/IBM_processed.csv',
    'data/processed/IEF_processed.csv',
    'data/processed/MO_processed.csv',
]
#%%
train_files, test_files = load_data_from_files(files, TRAIN_START_DATE, TRADE_END_DATE, columns, assign_to_bucket_columns, bucket_granularity=2)
#%%
processed_ibm = process_data(train_files['data/processed/IBM_processed.csv'], 'IBM')
processed_ief = process_data(train_files['data/processed/IEF_processed.csv'], 'IEF')
processed_mo = process_data(train_files['data/processed/MO_processed.csv'], 'MO')
processed = pd.concat([processed_ibm, processed_ief, processed_mo])

processed_full, stock_dimension, state_space = prepare_data(processed)
#%%
setup_seed(seed)
#%%
buy_cost_list = sell_cost_list = [0.001] * stock_dimension
num_stock_shares = [0] * stock_dimension

env_kwargs = {
    "hmax": 100,
    "initial_amount": 1000000,
    "num_stock_shares": num_stock_shares,
    "buy_cost_pct": buy_cost_list,
    "sell_cost_pct": sell_cost_list,
    "state_space": state_space,
    "stock_dim": stock_dimension,
    "tech_indicator_list": assign_to_bucket_columns,
    "action_space": stock_dimension,
    "reward_scaling": 1e-4
}

e_train_gym = StockTradingEnv(df = processed_full, **env_kwargs)
#%%
env = DummyVecEnv([lambda: e_train_gym])
#%%
model = TD3('MlpPolicy', env, verbose=0, tensorboard_log="./FinRL/tensorboard", learning_rate=linear_schedule(0.0001), seed=seed, policy_kwargs=policy_kwargs)
#%%
model.policy
#%%
test_ibm = process_data(test_files['data/processed/IBM_processed.csv'], 'IBM')
test_ief = process_data(test_files['data/processed/IEF_processed.csv'], 'IEF')
test_mo = process_data(test_files['data/processed/MO_processed.csv'], 'MO')
test_processed = pd.concat([test_ibm, test_ief, test_mo])

test_processed_full, _, _ = prepare_data(test_processed)
#%%
steps_in_episode = processed_full.shape[0] // len(train_files)
num_of_episodes = 1000
total_steps = num_of_episodes * steps_in_episode

callback = CustomCallbackFinRL(
                            eval_data=test_processed_full,
                            env_kwargs=env_kwargs,
                            eval_model_episodes_frequency=5,
                            best_model_save_path=f"FinRL/rl_models/{tb_log_name}",
                            save_model_episodes_frequency=10,
                            save_model_path=f"FinRL/rl_models/{tb_log_name}_last"
                            )

model.learn(total_timesteps=total_steps,
            tb_log_name=tb_log_name,
            callback=callback)
#%%
